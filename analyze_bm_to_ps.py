#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析COA数据：从BM（缩放前）到PS（缩放后）的变换
计算Xppm和Yppm参数
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from compute_ppm import CoordinateTransformSolver, plot_results

def load_bm_ps_data(excel_file):
    """
    从Excel文件加载BM和PS数据
    """
    # 读取BM sheet（缩放前）
    df_bm = pd.read_excel(excel_file, sheet_name='BM')
    print("BM (缩放前) 数据:")
    print(df_bm.head())
    
    # 读取PS sheet（缩放后）
    df_ps = pd.read_excel(excel_file, sheet_name='PS')
    print("\nPS (缩放后) 数据:")
    print(df_ps.head())
    
    # 跳过第一行（ID行），提取坐标数据
    bm_data = df_bm.iloc[1:].copy()
    ps_data = df_ps.iloc[1:].copy()
    
    # 提取3组坐标对
    coords_bm_1 = bm_data.iloc[:, [0, 1]].dropna().values.astype(float)  # N210246L00804
    coords_bm_2 = bm_data.iloc[:, [2, 3]].dropna().values.astype(float)  # N210246L00806
    coords_bm_3 = bm_data.iloc[:, [4, 5]].dropna().values.astype(float)  # N210246L00815
    
    coords_ps_1 = ps_data.iloc[:, [0, 1]].dropna().values.astype(float)  # N210246L00804
    coords_ps_2 = ps_data.iloc[:, [2, 3]].dropna().values.astype(float)  # N210246L00806
    coords_ps_3 = ps_data.iloc[:, [4, 5]].dropna().values.astype(float)  # N210246L00815
    
    print(f"\n数据统计:")
    print(f"N210246L00804: BM {len(coords_bm_1)} 点, PS {len(coords_ps_1)} 点")
    print(f"N210246L00806: BM {len(coords_bm_2)} 点, PS {len(coords_ps_2)} 点")
    print(f"N210246L00815: BM {len(coords_bm_3)} 点, PS {len(coords_ps_3)} 点")
    
    return {
        'BM': [coords_bm_1, coords_bm_2, coords_bm_3],
        'PS': [coords_ps_1, coords_ps_2, coords_ps_3],
        'IDs': ['N210246L00804', 'N210246L00806', 'N210246L00815']
    }

def analyze_bm_to_ps_transform(coords_bm, coords_ps, chip_id):
    """
    分析从BM到PS的变换参数
    """
    print(f"\n{'='*70}")
    print(f"分析芯片: {chip_id} (BM → PS)")
    print(f"{'='*70}")
    
    if len(coords_bm) != len(coords_ps):
        min_len = min(len(coords_bm), len(coords_ps))
        coords_bm = coords_bm[:min_len]
        coords_ps = coords_ps[:min_len]
        print(f"警告: 坐标点数量不匹配，使用前{min_len}个点")
    
    print(f"使用 {len(coords_bm)} 个坐标点对")
    
    # 基本统计
    print(f"\nBM (缩放前) 统计:")
    print(f"  X范围: [{coords_bm[:, 0].min():.2f}, {coords_bm[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_bm[:, 1].min():.2f}, {coords_bm[:, 1].max():.2f}]")
    print(f"  质心: ({coords_bm[:, 0].mean():.2f}, {coords_bm[:, 1].mean():.2f})")
    
    print(f"\nPS (缩放后) 统计:")
    print(f"  X范围: [{coords_ps[:, 0].min():.2f}, {coords_ps[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_ps[:, 1].min():.2f}, {coords_ps[:, 1].max():.2f}]")
    print(f"  质心: ({coords_ps[:, 0].mean():.2f}, {coords_ps[:, 1].mean():.2f})")
    
    # 计算差异
    diff = coords_ps - coords_bm
    print(f"\n坐标差异分析:")
    print(f"  X差异: 平均={diff[:, 0].mean():.2e}, 标准差={diff[:, 0].std():.2e}")
    print(f"  Y差异: 平均={diff[:, 1].mean():.2e}, 标准差={diff[:, 1].std():.2e}")
    print(f"  最大X差异: {diff[:, 0].max():.2e}, 最小X差异: {diff[:, 0].min():.2e}")
    print(f"  最大Y差异: {diff[:, 1].max():.2e}, 最小Y差异: {diff[:, 1].min():.2e}")
    
    # 估算伸缩比例
    # 使用标准差比例作为初步估算
    scale_x_std = coords_ps[:, 0].std() / coords_bm[:, 0].std() if coords_bm[:, 0].std() > 0 else 1.0
    scale_y_std = coords_ps[:, 1].std() / coords_bm[:, 1].std() if coords_bm[:, 1].std() > 0 else 1.0
    
    # 使用范围比例作为另一种估算
    range_bm_x = coords_bm[:, 0].max() - coords_bm[:, 0].min()
    range_ps_x = coords_ps[:, 0].max() - coords_ps[:, 0].min()
    range_bm_y = coords_bm[:, 1].max() - coords_bm[:, 1].min()
    range_ps_y = coords_ps[:, 1].max() - coords_ps[:, 1].min()
    
    scale_x_range = range_ps_x / range_bm_x if range_bm_x > 0 else 1.0
    scale_y_range = range_ps_y / range_bm_y if range_bm_y > 0 else 1.0
    
    print(f"\n初步伸缩估算:")
    print(f"  X方向 (标准差法): {scale_x_std:.8f} (ppm: {(scale_x_std-1)*1e6:.1f})")
    print(f"  Y方向 (标准差法): {scale_y_std:.8f} (ppm: {(scale_y_std-1)*1e6:.1f})")
    print(f"  X方向 (范围法): {scale_x_range:.8f} (ppm: {(scale_x_range-1)*1e6:.1f})")
    print(f"  Y方向 (范围法): {scale_y_range:.8f} (ppm: {(scale_y_range-1)*1e6:.1f})")
    
    # 使用求解器精确计算
    solver = CoordinateTransformSolver()
    
    if len(coords_bm) < 3:
        print("警告: 点数少于3个，无法求解所有参数")
        return None
    
    print(f"\n正在求解精确变换参数...")
    success, results = solver.solve(coords_bm, coords_ps)
    
    if success:
        print(f"\n[SUCCESS] 求解成功！")
        print(f"精确变换参数:")
        print(f"  Xppm: {results['Xppm']:.2e} ({results['Xppm']*1e6:.2f} ppm)")
        print(f"  Yppm: {results['Yppm']:.2e} ({results['Yppm']*1e6:.2f} ppm)")
        print(f"  旋转: {results['rotation_deg']:.6f}° ({results['rotation_rad']:.2e} rad)")
        print(f"  平移X: {results['translation_x']:.6f}")
        print(f"  平移Y: {results['translation_y']:.6f}")
        print(f"  RMS误差: {results['rms_error']:.2e}")
        print(f"  最大误差: {results['max_error']:.2e}")
        print(f"  迭代次数: {results['iterations']}")
        
        # 验证解的准确性
        is_valid, errors = solver.validate_solution(coords_bm, coords_ps, tolerance=0.01)
        print(f"  解的有效性 (容差0.01): {is_valid}")
        print(f"  平均点误差: {np.mean(errors):.2e}")
        print(f"  最大点误差: {np.max(errors):.2e}")
        
        # 显示详细的点误差分布
        print(f"\n点误差分布:")
        print(f"  < 0.001: {np.sum(errors < 0.001)} 点")
        print(f"  < 0.01:  {np.sum(errors < 0.01)} 点")
        print(f"  < 0.1:   {np.sum(errors < 0.1)} 点")
        print(f"  >= 0.1:  {np.sum(errors >= 0.1)} 点")
        
        return results
    else:
        print(f"\n[FAILED] 求解失败: {results['message']}")
        return None

def main():
    excel_file = "COA_20240809_095307.xlsx"
    
    # 加载BM和PS数据
    data = load_bm_ps_data(excel_file)
    
    results_summary = []
    
    # 分析每个芯片的BM到PS变换
    for i, chip_id in enumerate(data['IDs']):
        coords_bm = data['BM'][i]
        coords_ps = data['PS'][i]
        
        result = analyze_bm_to_ps_transform(coords_bm, coords_ps, chip_id)
        
        if result:
            results_summary.append({
                'chip_id': chip_id,
                'Xppm': result['Xppm'],
                'Yppm': result['Yppm'],
                'Xppm_value': result['Xppm'] * 1e6,  # 转换为ppm单位
                'Yppm_value': result['Yppm'] * 1e6,  # 转换为ppm单位
                'rotation_deg': result['rotation_deg'],
                'translation_x': result['translation_x'],
                'translation_y': result['translation_y'],
                'rms_error': result['rms_error'],
                'max_error': result['max_error']
            })
    
    # 汇总结果
    if results_summary:
        print(f"\n{'='*80}")
        print("BM → PS 变换参数汇总")
        print(f"{'='*80}")
        
        df_summary = pd.DataFrame(results_summary)
        
        # 显示主要结果
        print("\n主要结果 (PPM值):")
        for _, row in df_summary.iterrows():
            print(f"{row['chip_id']:15s}: Xppm = {row['Xppm_value']:8.2f} ppm, Yppm = {row['Yppm_value']:8.2f} ppm")
        
        print(f"\n详细参数表:")
        print(df_summary[['chip_id', 'Xppm_value', 'Yppm_value', 'rotation_deg', 'rms_error']].to_string(index=False, float_format='%.3f'))
        
        # 统计分析
        print(f"\nXppm统计 (ppm):")
        print(f"  平均值: {df_summary['Xppm_value'].mean():.2f} ppm")
        print(f"  标准差: {df_summary['Xppm_value'].std():.2f} ppm")
        print(f"  范围: [{df_summary['Xppm_value'].min():.2f}, {df_summary['Xppm_value'].max():.2f}] ppm")
        
        print(f"\nYppm统计 (ppm):")
        print(f"  平均值: {df_summary['Yppm_value'].mean():.2f} ppm")
        print(f"  标准差: {df_summary['Yppm_value'].std():.2f} ppm")
        print(f"  范围: [{df_summary['Yppm_value'].min():.2f}, {df_summary['Yppm_value'].max():.2f}] ppm")
        
        # 保存结果
        output_file = "BM_to_PS_analysis_results.xlsx"
        df_summary.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 生成可视化图表
        create_visualization(data, results_summary)

def create_visualization(data, results_summary):
    """
    创建可视化图表
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('BM → PS 变换分析结果', fontsize=16)
    
    # PPM值柱状图
    chip_ids = [r['chip_id'] for r in results_summary]
    xppm_values = [r['Xppm_value'] for r in results_summary]
    yppm_values = [r['Yppm_value'] for r in results_summary]
    
    x_pos = np.arange(len(chip_ids))
    
    axes[0, 0].bar(x_pos - 0.2, xppm_values, 0.4, label='Xppm', alpha=0.7)
    axes[0, 0].bar(x_pos + 0.2, yppm_values, 0.4, label='Yppm', alpha=0.7)
    axes[0, 0].set_xlabel('芯片ID')
    axes[0, 0].set_ylabel('PPM值')
    axes[0, 0].set_title('各芯片PPM值对比')
    axes[0, 0].set_xticks(x_pos)
    axes[0, 0].set_xticklabels([id.split('L')[1] for id in chip_ids], rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 误差分析
    rms_errors = [r['rms_error'] for r in results_summary]
    max_errors = [r['max_error'] for r in results_summary]
    
    axes[0, 1].bar(x_pos - 0.2, rms_errors, 0.4, label='RMS误差', alpha=0.7)
    axes[0, 1].bar(x_pos + 0.2, max_errors, 0.4, label='最大误差', alpha=0.7)
    axes[0, 1].set_xlabel('芯片ID')
    axes[0, 1].set_ylabel('误差')
    axes[0, 1].set_title('变换误差分析')
    axes[0, 1].set_xticks(x_pos)
    axes[0, 1].set_xticklabels([id.split('L')[1] for id in chip_ids], rotation=45)
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 坐标散点图 (第一个芯片作为示例)
    if len(data['BM']) > 0:
        coords_bm = data['BM'][0]
        coords_ps = data['PS'][0]
        
        axes[1, 0].scatter(coords_bm[:, 0], coords_bm[:, 1], alpha=0.6, label='BM (缩放前)', s=30)
        axes[1, 0].scatter(coords_ps[:, 0], coords_ps[:, 1], alpha=0.6, label='PS (缩放后)', s=30)
        axes[1, 0].set_xlabel('X坐标')
        axes[1, 0].set_ylabel('Y坐标')
        axes[1, 0].set_title(f'坐标对比 - {chip_ids[0]}')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].axis('equal')
    
    # PPM值散点图
    axes[1, 1].scatter(xppm_values, yppm_values, s=100, alpha=0.7)
    for i, chip_id in enumerate(chip_ids):
        axes[1, 1].annotate(chip_id.split('L')[1], 
                           (xppm_values[i], yppm_values[i]), 
                           xytext=(5, 5), textcoords='offset points')
    axes[1, 1].set_xlabel('Xppm (ppm)')
    axes[1, 1].set_ylabel('Yppm (ppm)')
    axes[1, 1].set_title('Xppm vs Yppm 分布')
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].axhline(y=0, color='k', linestyle='--', alpha=0.3)
    axes[1, 1].axvline(x=0, color='k', linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('BM_to_PS_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("可视化图表已保存为: BM_to_PS_analysis.png")

if __name__ == "__main__":
    main()
