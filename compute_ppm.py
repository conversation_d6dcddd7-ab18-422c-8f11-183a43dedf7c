import numpy as np
from scipy.optimize import least_squares
import pandas as pd
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional

class CoordinateTransformSolver:
    """
    解决坐标变换参数估计问题

    变换顺序：伸缩 -> 旋转 -> 平移
    x' = (x * (1 + Xppm)) * cos(θ) - (y * (1 + Yppm)) * sin(θ) + tx
    y' = (x * (1 + Xppm)) * sin(θ) + (y * (1 + Yppm)) * cos(θ) + ty

    参数：[Xppm, Yppm, θ, tx, ty]
    """

    def __init__(self):
        self.params = None
        self.residuals = None
        self.success = False

    def transform_points(self, points_A: np.ndarray, params: np.ndarray) -> np.ndarray:
        """
        应用变换参数到坐标点

        Args:
            points_A: 原始坐标点 (N, 2)
            params: 变换参数 [Xppm, Yppm, theta, tx, ty]

        Returns:
            变换后的坐标点 (N, 2)
        """
        Xppm, Yppm, theta, tx, ty = params

        # 伸缩变换
        x_scaled = points_A[:, 0] * (1 + Xppm)
        y_scaled = points_A[:, 1] * (1 + Yppm)

        # 旋转变换
        cos_theta = np.cos(theta)
        sin_theta = np.sin(theta)

        x_rotated = x_scaled * cos_theta - y_scaled * sin_theta
        y_rotated = x_scaled * sin_theta + y_scaled * cos_theta

        # 平移变换
        x_final = x_rotated + tx
        y_final = y_rotated + ty

        return np.column_stack([x_final, y_final])

    def residual_function(self, params: np.ndarray, points_A: np.ndarray, points_B: np.ndarray) -> np.ndarray:
        """
        计算残差函数

        Args:
            params: 变换参数 [Xppm, Yppm, theta, tx, ty]
            points_A: 原始坐标点 (N, 2)
            points_B: 目标坐标点 (N, 2)

        Returns:
            残差向量 (2N,)
        """
        transformed_points = self.transform_points(points_A, params)
        residuals = (transformed_points - points_B).flatten()
        return residuals

    def solve(self, points_A: np.ndarray, points_B: np.ndarray,
              initial_guess: Optional[np.ndarray] = None) -> Tuple[bool, dict]:
        """
        求解变换参数

        Args:
            points_A: 原始坐标点 (N, 2)
            points_B: 目标坐标点 (N, 2)
            initial_guess: 初始猜测值 [Xppm, Yppm, theta, tx, ty]

        Returns:
            (success, results_dict)
        """
        if points_A.shape != points_B.shape:
            raise ValueError("点集A和B的形状必须相同")

        if points_A.shape[0] < 3:
            raise ValueError("至少需要3个点来求解5个参数")

        # 初始猜测
        if initial_guess is None:
            # 计算质心差作为平移的初始猜测
            centroid_A = np.mean(points_A, axis=0)
            centroid_B = np.mean(points_B, axis=0)
            initial_guess = np.array([0.0, 0.0, 0.0,
                                    centroid_B[0] - centroid_A[0],
                                    centroid_B[1] - centroid_A[1]])

        # 使用最小二乘法求解
        result = least_squares(
            self.residual_function,
            initial_guess,
            args=(points_A, points_B),
            method='lm'  # Levenberg-Marquardt算法
        )

        self.success = result.success
        self.params = result.x
        self.residuals = result.fun

        # 计算统计信息
        rms_error = np.sqrt(np.mean(result.fun**2))
        max_error = np.max(np.abs(result.fun))

        results = {
            'success': result.success,
            'Xppm': self.params[0],
            'Yppm': self.params[1],
            'rotation_rad': self.params[2],
            'rotation_deg': np.degrees(self.params[2]),
            'translation_x': self.params[3],
            'translation_y': self.params[4],
            'rms_error': rms_error,
            'max_error': max_error,
            'iterations': result.nfev,
            'message': result.message
        }

        return self.success, results

    def validate_solution(self, points_A: np.ndarray, points_B: np.ndarray,
                         tolerance: float = 1e-6) -> Tuple[bool, np.ndarray]:
        """
        验证解的准确性

        Args:
            points_A: 原始坐标点
            points_B: 目标坐标点
            tolerance: 容差

        Returns:
            (is_valid, errors)
        """
        if self.params is None:
            raise ValueError("请先调用solve()方法")

        transformed_points = self.transform_points(points_A, self.params)
        errors = np.linalg.norm(transformed_points - points_B, axis=1)
        is_valid = np.all(errors < tolerance)

        return is_valid, errors


def load_coordinates_from_excel(file_path: str, sheet_name: str = None) -> Tuple[np.ndarray, np.ndarray]:
    """
    从Excel文件加载坐标数据

    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称

    Returns:
        (points_A, points_B)
    """
    df = pd.read_excel(file_path, sheet_name=sheet_name)

    # 假设Excel格式为: X_A, Y_A, X_B, Y_B
    if df.shape[1] < 4:
        raise ValueError("Excel文件至少需要4列：X_A, Y_A, X_B, Y_B")

    points_A = df.iloc[:, :2].values
    points_B = df.iloc[:, 2:4].values

    return points_A, points_B


def plot_results(points_A: np.ndarray, points_B: np.ndarray,
                transformed_points: np.ndarray, title: str = "坐标变换结果"):
    """
    绘制变换结果
    """
    plt.figure(figsize=(12, 8))

    plt.subplot(1, 2, 1)
    plt.scatter(points_A[:, 0], points_A[:, 1], c='blue', label='原始点A', alpha=0.7)
    plt.scatter(points_B[:, 0], points_B[:, 1], c='red', label='目标点B', alpha=0.7)
    plt.legend()
    plt.title('原始数据')
    plt.axis('equal')
    plt.grid(True)

    plt.subplot(1, 2, 2)
    plt.scatter(transformed_points[:, 0], transformed_points[:, 1],
                c='green', label='变换后的A', alpha=0.7)
    plt.scatter(points_B[:, 0], points_B[:, 1], c='red', label='目标点B', alpha=0.7)

    # 绘制误差向量
    for i in range(len(points_B)):
        plt.plot([transformed_points[i, 0], points_B[i, 0]],
                [transformed_points[i, 1], points_B[i, 1]],
                'k--', alpha=0.3)

    plt.legend()
    plt.title('变换结果对比')
    plt.axis('equal')
    plt.grid(True)

    plt.tight_layout()
    plt.show()


# 示例使用
if __name__ == "__main__":
    # 创建示例数据
    np.random.seed(42)

    # 真实参数
    true_Xppm = 50e-6  # 50 ppm
    true_Yppm = -30e-6  # -30 ppm
    true_theta = 0.001  # 约0.057度
    true_tx = 0.1
    true_ty = -0.05

    # 生成原始点
    n_points = 10
    points_A = np.random.uniform(-10, 10, (n_points, 2))

    # 应用真实变换生成目标点
    solver = CoordinateTransformSolver()
    true_params = np.array([true_Xppm, true_Yppm, true_theta, true_tx, true_ty])
    points_B = solver.transform_points(points_A, true_params)

    # 添加少量噪声
    points_B += np.random.normal(0, 0.001, points_B.shape)

    # 求解参数
    success, results = solver.solve(points_A, points_B)

    if success:
        print("求解成功！")
        print(f"Xppm: {results['Xppm']:.2e} (真实值: {true_Xppm:.2e})")
        print(f"Yppm: {results['Yppm']:.2e} (真实值: {true_Yppm:.2e})")
        print(f"旋转角度: {results['rotation_deg']:.6f}° (真实值: {np.degrees(true_theta):.6f}°)")
        print(f"平移X: {results['translation_x']:.6f} (真实值: {true_tx:.6f})")
        print(f"平移Y: {results['translation_y']:.6f} (真实值: {true_ty:.6f})")
        print(f"RMS误差: {results['rms_error']:.2e}")
        print(f"最大误差: {results['max_error']:.2e}")

        # 验证解
        is_valid, errors = solver.validate_solution(points_A, points_B, tolerance=0.01)
        print(f"解的有效性: {is_valid}")
        print(f"各点误差: {errors}")

        # 绘制结果
        transformed_points = solver.transform_points(points_A, solver.params)
        plot_results(points_A, points_B, transformed_points)
    else:
        print("求解失败！")
        print(f"错误信息: {results['message']}")