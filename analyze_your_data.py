#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析您的COA数据的脚本
Excel格式：3组坐标对 (N210246L00804, N210246L00806, N210246L00815)
每组包含X和Y坐标
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from compute_ppm import CoordinateTransformSolver, plot_results

def load_coa_data(excel_file):
    """
    加载COA Excel数据
    """
    df = pd.read_excel(excel_file)
    print("原始数据:")
    print(df.head())
    
    # 跳过第一行（标题行）
    data = df.iloc[1:].copy()
    
    # 提取3组坐标
    coords_1 = data.iloc[:, [0, 1]].dropna().values.astype(float)  # N210246L00804
    coords_2 = data.iloc[:, [2, 3]].dropna().values.astype(float)  # N210246L00806  
    coords_3 = data.iloc[:, [4, 5]].dropna().values.astype(float)  # N210246L00815
    
    print(f"\n数据统计:")
    print(f"坐标组1 (N210246L00804): {len(coords_1)} 个点")
    print(f"坐标组2 (N210246L00806): {len(coords_2)} 个点")
    print(f"坐标组3 (N210246L00815): {len(coords_3)} 个点")
    
    return coords_1, coords_2, coords_3

def analyze_coordinate_pairs(coords_A, coords_B, pair_name):
    """
    分析一对坐标组
    """
    print(f"\n{'='*60}")
    print(f"分析坐标对: {pair_name}")
    print(f"{'='*60}")
    
    if len(coords_A) != len(coords_B):
        min_len = min(len(coords_A), len(coords_B))
        coords_A = coords_A[:min_len]
        coords_B = coords_B[:min_len]
        print(f"警告: 坐标点数量不匹配，使用前{min_len}个点")
    
    print(f"使用 {len(coords_A)} 个坐标点对")
    
    # 基本统计
    print(f"\n坐标A统计:")
    print(f"  X范围: [{coords_A[:, 0].min():.2f}, {coords_A[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_A[:, 1].min():.2f}, {coords_A[:, 1].max():.2f}]")
    print(f"  质心: ({coords_A[:, 0].mean():.2f}, {coords_A[:, 1].mean():.2f})")
    
    print(f"\n坐标B统计:")
    print(f"  X范围: [{coords_B[:, 0].min():.2f}, {coords_B[:, 0].max():.2f}]")
    print(f"  Y范围: [{coords_B[:, 1].min():.2f}, {coords_B[:, 1].max():.2f}]")
    print(f"  质心: ({coords_B[:, 0].mean():.2f}, {coords_B[:, 1].mean():.2f})")
    
    # 计算初步差异
    diff = coords_B - coords_A
    print(f"\n初步差异分析:")
    print(f"  X差异: 平均={diff[:, 0].mean():.2e}, 标准差={diff[:, 0].std():.2e}")
    print(f"  Y差异: 平均={diff[:, 1].mean():.2e}, 标准差={diff[:, 1].std():.2e}")
    
    # 估算初步的伸缩比例
    scale_x = coords_B[:, 0].std() / coords_A[:, 0].std() if coords_A[:, 0].std() > 0 else 1.0
    scale_y = coords_B[:, 1].std() / coords_A[:, 1].std() if coords_A[:, 1].std() > 0 else 1.0
    
    print(f"\n初步伸缩估算:")
    print(f"  X方向伸缩比: {scale_x:.8f} (ppm: {(scale_x-1)*1e6:.1f})")
    print(f"  Y方向伸缩比: {scale_y:.8f} (ppm: {(scale_y-1)*1e6:.1f})")
    
    # 使用求解器
    solver = CoordinateTransformSolver()
    
    if len(coords_A) < 3:
        print("警告: 点数少于3个，无法求解所有参数")
        return None
    
    print(f"\n正在求解变换参数...")
    success, results = solver.solve(coords_A, coords_B)
    
    if success:
        print(f"\n[SUCCESS] 求解成功！")
        print(f"变换参数:")
        print(f"  Xppm: {results['Xppm']:.2e} ({results['Xppm']*1e6:.2f} ppm)")
        print(f"  Yppm: {results['Yppm']:.2e} ({results['Yppm']*1e6:.2f} ppm)")
        print(f"  旋转: {results['rotation_deg']:.6f}° ({results['rotation_rad']:.2e} rad)")
        print(f"  平移X: {results['translation_x']:.6f}")
        print(f"  平移Y: {results['translation_y']:.6f}")
        print(f"  RMS误差: {results['rms_error']:.2e}")
        print(f"  最大误差: {results['max_error']:.2e}")
        
        # 验证
        is_valid, errors = solver.validate_solution(coords_A, coords_B, tolerance=0.001)
        print(f"  解的有效性: {is_valid}")
        print(f"  平均点误差: {np.mean(errors):.2e}")
        
        return results
    else:
        print(f"\n[FAILED] 求解失败: {results['message']}")
        return None

def main():
    excel_file = "COA_20240809_095307.xlsx"
    
    # 加载数据
    coords_1, coords_2, coords_3 = load_coa_data(excel_file)
    
    # 分析所有可能的坐标对组合
    pairs = [
        (coords_1, coords_2, "N210246L00804 → N210246L00806"),
        (coords_1, coords_3, "N210246L00804 → N210246L00815"), 
        (coords_2, coords_3, "N210246L00806 → N210246L00815"),
        (coords_2, coords_1, "N210246L00806 → N210246L00804"),
        (coords_3, coords_1, "N210246L00815 → N210246L00804"),
        (coords_3, coords_2, "N210246L00815 → N210246L00806")
    ]
    
    results_summary = []
    
    for coords_A, coords_B, pair_name in pairs:
        result = analyze_coordinate_pairs(coords_A, coords_B, pair_name)
        if result:
            results_summary.append({
                'pair': pair_name,
                'Xppm': result['Xppm'],
                'Yppm': result['Yppm'],
                'rotation_deg': result['rotation_deg'],
                'translation_x': result['translation_x'],
                'translation_y': result['translation_y'],
                'rms_error': result['rms_error'],
                'max_error': result['max_error']
            })
    
    # 汇总结果
    if results_summary:
        print(f"\n{'='*80}")
        print("所有坐标对分析结果汇总")
        print(f"{'='*80}")
        
        df_summary = pd.DataFrame(results_summary)
        print(df_summary.to_string(index=False, float_format='%.2e'))
        
        # 保存结果
        output_file = "coordinate_analysis_results.xlsx"
        df_summary.to_excel(output_file, index=False)
        print(f"\n结果已保存到: {output_file}")
        
        # 统计分析
        print(f"\nXppm统计:")
        print(f"  平均值: {df_summary['Xppm'].mean():.2e} ({df_summary['Xppm'].mean()*1e6:.2f} ppm)")
        print(f"  标准差: {df_summary['Xppm'].std():.2e}")
        print(f"  范围: [{df_summary['Xppm'].min():.2e}, {df_summary['Xppm'].max():.2e}]")
        
        print(f"\nYppm统计:")
        print(f"  平均值: {df_summary['Yppm'].mean():.2e} ({df_summary['Yppm'].mean()*1e6:.2f} ppm)")
        print(f"  标准差: {df_summary['Yppm'].std():.2e}")
        print(f"  范围: [{df_summary['Yppm'].min():.2e}, {df_summary['Yppm'].max():.2e}]")

if __name__ == "__main__":
    main()
