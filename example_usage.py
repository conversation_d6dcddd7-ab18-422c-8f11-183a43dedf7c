#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例：从Excel文件读取坐标数据并计算PPM参数

使用方法：
1. 确保Excel文件格式为：X_A, Y_A, X_B, Y_B（4列）
2. 运行此脚本
"""

import numpy as np
import pandas as pd
from compute_ppm import CoordinateTransformSolver, load_coordinates_from_excel, plot_results

def main():
    # Excel文件路径
    excel_file = "COA_20240809_095307.xlsx"
    
    try:
        # 方法1：如果Excel文件格式标准（4列：X_A, Y_A, X_B, Y_B）
        print("正在读取Excel文件...")
        points_A, points_B = load_coordinates_from_excel(excel_file)
        
    except Exception as e:
        print(f"使用标准格式读取失败: {e}")
        print("请手动指定数据格式...")
        
        # 方法2：手动读取和处理Excel数据
        df = pd.read_excel(excel_file)
        print("Excel文件列名:")
        print(df.columns.tolist())
        print("\n前5行数据:")
        print(df.head())
        
        # 请根据实际Excel格式修改以下代码
        # 示例：假设列名为 'X1', 'Y1', 'X2', 'Y2'
        # points_A = df[['X1', 'Y1']].values
        # points_B = df[['X2', 'Y2']].values
        
        print("\n请根据您的Excel格式修改example_usage.py中的列名")
        return
    
    print(f"成功读取 {len(points_A)} 个坐标点对")
    print(f"点A范围: X[{points_A[:, 0].min():.3f}, {points_A[:, 0].max():.3f}], "
          f"Y[{points_A[:, 1].min():.3f}, {points_A[:, 1].max():.3f}]")
    print(f"点B范围: X[{points_B[:, 0].min():.3f}, {points_B[:, 0].max():.3f}], "
          f"Y[{points_B[:, 1].min():.3f}, {points_B[:, 1].max():.3f}]")
    
    # 创建求解器
    solver = CoordinateTransformSolver()
    
    # 求解变换参数
    print("\n正在求解变换参数...")
    success, results = solver.solve(points_A, points_B)
    
    if success:
        print("\n✓ 求解成功！")
        print("="*50)
        print("变换参数结果:")
        print(f"  Xppm (X方向伸缩): {results['Xppm']:.2e} ({results['Xppm']*1e6:.1f} ppm)")
        print(f"  Yppm (Y方向伸缩): {results['Yppm']:.2e} ({results['Yppm']*1e6:.1f} ppm)")
        print(f"  旋转角度: {results['rotation_deg']:.6f}° ({results['rotation_rad']:.2e} rad)")
        print(f"  X方向平移: {results['translation_x']:.6f}")
        print(f"  Y方向平移: {results['translation_y']:.6f}")
        print("="*50)
        print("精度评估:")
        print(f"  RMS误差: {results['rms_error']:.2e}")
        print(f"  最大误差: {results['max_error']:.2e}")
        print(f"  迭代次数: {results['iterations']}")
        print("="*50)
        
        # 验证解的准确性
        is_valid, errors = solver.validate_solution(points_A, points_B, tolerance=0.001)
        print(f"解的有效性 (容差0.001): {is_valid}")
        print(f"各点误差统计:")
        print(f"  平均误差: {np.mean(errors):.2e}")
        print(f"  最大误差: {np.max(errors):.2e}")
        print(f"  最小误差: {np.min(errors):.2e}")
        
        # 显示详细的点对点误差
        print(f"\n各点详细误差:")
        for i, error in enumerate(errors):
            print(f"  点{i+1}: {error:.2e}")
        
        # 绘制结果
        print("\n正在生成可视化图表...")
        transformed_points = solver.transform_points(points_A, solver.params)
        plot_results(points_A, points_B, transformed_points, "坐标变换结果")
        
        # 保存结果到文件
        results_df = pd.DataFrame({
            '点编号': range(1, len(points_A) + 1),
            'A_X': points_A[:, 0],
            'A_Y': points_A[:, 1],
            'B_X': points_B[:, 0],
            'B_Y': points_B[:, 1],
            '变换后_X': transformed_points[:, 0],
            '变换后_Y': transformed_points[:, 1],
            '误差': errors
        })
        
        output_file = "transformation_results.xlsx"
        with pd.ExcelWriter(output_file) as writer:
            results_df.to_excel(writer, sheet_name='点对点结果', index=False)
            
            # 保存参数摘要
            params_df = pd.DataFrame({
                '参数': ['Xppm', 'Yppm', '旋转角度(度)', 'X平移', 'Y平移', 'RMS误差', '最大误差'],
                '数值': [results['Xppm'], results['Yppm'], results['rotation_deg'], 
                        results['translation_x'], results['translation_y'], 
                        results['rms_error'], results['max_error']],
                '单位': ['', '', '度', '', '', '', '']
            })
            params_df.to_excel(writer, sheet_name='参数摘要', index=False)
        
        print(f"结果已保存到: {output_file}")
        
    else:
        print("\n✗ 求解失败！")
        print(f"错误信息: {results['message']}")
        print("可能的原因:")
        print("1. 数据点数量不足（至少需要3个点）")
        print("2. 数据质量问题（噪声过大、异常值等）")
        print("3. 变换模型不适用于当前数据")


if __name__ == "__main__":
    main()
